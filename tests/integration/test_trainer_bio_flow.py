import pytest
import json
from unittest.mock import patch, AsyncMock

from fastapi.testclient import TestClient

from app.main import app # Your FastAPI app instance
from app.models.trainer_bio import TrainerBio, BiosSpecialty, BiosCertification, TrainerNameExtraction, TrainerBioSearchResponse
from app.models.intents_handler import IntentResponse # Assuming this model exists for intents_handler output


# It's good practice to use a TestClient fixture if you have multiple test files needing it
# For this single file, initializing directly is also fine.
client = TestClient(app)

# Mock data for trainer bios API
MOCK_SARAH_G_BIO = TrainerBio(
    id="bio123",
    partyId="party789",
    employeeId="empSarahG",
    firstName="Sarah",
    lastName="G.",
    description="An experienced and passionate fitness coach specializing in strength training and yoga.",
    mediaId="img456",
    isConsentAccepted=True,
    clubIds=["club1", "club2"],
    specialties=[BiosSpecialty(id="spec1", title="Strength Training"), BiosSpecialty(id="spec2", title="Yoga")],
    certifications=[BiosCertification(id="cert1", title="Certified Personal Trainer"), BiosCertification(id="cert2", title="Yoga Alliance RYT 200")],
    isPublished=True,
    email="<EMAIL>",
    profilePath="sarahg"
)

MOCK_CHRIS_P_BIO = TrainerBio(
    id="bio789",
    partyId="partyChrisP",
    employeeId="empChrisP",
    firstName="Chris",
    lastName="P.",
    description="Dedicated to helping clients achieve their mobility and endurance goals.",
    mediaId="img789",
    isConsentAccepted=True,
    clubIds=["club3"],
    specialties=[BiosSpecialty(id="spec3", title="Mobility"), BiosSpecialty(id="spec4", title="Endurance Running")],
    certifications=[BiosCertification(id="cert3", title="Functional Movement Specialist")],
    isPublished=True,
    email="<EMAIL>",
    profilePath="chrisp"
)

MOCK_CHRIS_D_BIO = TrainerBio(
    id="bioChrisD",
    partyId="partyChrisD",
    employeeId="empChrisD",
    firstName="Chris",
    lastName="D.",
    description="Focuses on holistic wellness and nutritional guidance.",
    isConsentAccepted=True,
    clubIds=["club1"],
    specialties=[BiosSpecialty(id="spec5", title="Nutrition"), BiosSpecialty(id="spec6", title="Wellness Coaching")],
    isPublished=True,
    profilePath="chrisd"
)


DEFAULT_SCRATCH_PAD = {
    "name": "Test User",
    "conversationId": "integ-test-convo-123",
    "partyId": 12345,
    "memberId": 67890,
    "homeClub": "club1",
    "homeClubName": "Test Club",
    "todaysDate": "2024-07-30T10:00:00.000Z",
    "todaysWeekday": "Tuesday",
}


@pytest.mark.asyncio
async def test_trainer_bio_successful_retrieval_single_match():
    """
    Tests the full flow for a successful trainer bio query with a single match.
    """
    question = "Tell me about Sarah G."
    
    # 1. Mock Intent Detection (intents_handler)
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.99)
    
    # 2. Mock Trainer Name Extraction (trainer_name_extractor_handler)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="Sarah G.")
    
    # 3. Mock Scheduling.Bios.Api response (via httpx.AsyncClient.post in trainer_bio_repository)
    mock_bios_api_response_data = TrainerBioSearchResponse(results=[MOCK_SARAH_G_BIO], count=1)
    
    # 4. Mock LLM response for formatting bio (trainer_bio_handler)
    # The LLM will receive the bio data and produce a summary.
    # We're mocking the final output of the LLM based on the Jinja template + bio data.
    expected_llm_summary = (
        f"Here's what I found for Sarah G.:\n"
        f"An experienced and passionate fitness coach specializing in strength training and yoga.\n\n"
        f"Their specialties include:\n- Strength Training\n- Yoga\n\n"
        f"They hold certifications such as:\n- Certified Personal Trainer\n- Yoga Alliance RYT 200"
    )
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_llm_summary
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])


    with patch("app.handlers.intents_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_intent_response)) as mock_intent_openai, \
         patch("app.handlers.trainer_name_extractor_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_trainer_name_extraction)) as mock_name_extract_openai, \
         patch("app.repositories.trainer_bio_repository.httpx.AsyncClient.post") as mock_bios_api_post, \
         patch("app.handlers.trainer_bio_handler.OpenAIClient.chat_create", AsyncMock(return_value=mock_llm_final_response)) as mock_bio_format_openai:

        # Configure the mock for httpx.post (used by search_trainer_bio_by_name)
        mock_bios_api_post.return_value = AsyncMock(
            raise_for_status=lambda: None,
            json=lambda: mock_bios_api_response_data.model_dump() # Simulate the JSON response from the external API
        )

        payload = {
            "question": question,
            "history": [],
            "scratch_pad": DEFAULT_SCRATCH_PAD
        }
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()

    assert data["answer"] == expected_llm_summary
    assert data["path"] == "trainer_bio_query_result"
    assert not data.get("references") # No references expected for this flow yet

    mock_intent_openai.assert_called_once()
    mock_name_extract_openai.assert_called_once()
    
    # Assert that the bios API was called with the extracted name
    mock_bios_api_post.assert_called_once()
    called_bios_api_payload = mock_bios_api_post.call_args[1]['json'] # or call_args.kwargs['json']
    assert called_bios_api_payload['query'] == "Sarah G."
    
    mock_bio_format_openai.assert_called_once()
    # You can also inspect the messages sent to the OpenAIClient for bio formatting
    # to ensure the correct trainer data was passed in the system prompt.
    bio_format_messages = mock_bio_format_openai.call_args[1]['messages']
    system_prompt_content = bio_format_messages[0]['content'] # System prompt is the first message
    assert "Here's what I found for Sarah G.:" in system_prompt_content # Based on Jinja logic for single result
    assert MOCK_SARAH_G_BIO.description in system_prompt_content
    assert MOCK_SARAH_G_BIO.specialties[0].title in system_prompt_content


@pytest.mark.asyncio
async def test_trainer_bio_not_found():
    question = "Tell me about NonExistent Trainer."
    
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.98)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="NonExistent Trainer")
    mock_bios_api_response_data = TrainerBioSearchResponse(results=[], count=0) # API finds nothing
    
    expected_llm_not_found_msg = "I couldn't find any information for a trainer named \"NonExistent Trainer\". 🙁\nWould you like to try a different name, or perhaps check the spelling?"
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_llm_not_found_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    with patch("app.handlers.intents_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_intent_response)), \
         patch("app.handlers.trainer_name_extractor_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_trainer_name_extraction)), \
         patch("app.repositories.trainer_bio_repository.httpx.AsyncClient.post") as mock_bios_api_post, \
         patch("app.handlers.trainer_bio_handler.OpenAIClient.chat_create", AsyncMock(return_value=mock_llm_final_response)) as mock_bio_format_openai:
        
        mock_bios_api_post.return_value = AsyncMock(
            raise_for_status=lambda: None,
            json=lambda: mock_bios_api_response_data.model_dump()
        )

        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_llm_not_found_msg
    assert data["path"] == "trainer_bio_query_result" # Path might be same, content differs
    
    mock_bios_api_post.assert_called_once_with(
        f"{os.getenv('SCHEDULING_BIOS_API_BASE_URL', 'http://mockbios.api').rstrip('/')}/v2/bios/search", # Ensure base URL is consistent
        headers=pytest.approx({ # Use approx for headers as order might not matter or exact keys
            "ocp-apim-subscription-key": os.getenv("SCHEDULING_BIOS_API_KEY", "mock_key"),
            "Content-Type": "application/json",
            "Accept": "application/json",
        }),
        json={"query": "NonExistent Trainer", "isPublished": True, "take": 3},
        timeout=15.0
    )
    bio_format_messages = mock_bio_format_openai.call_args[1]['messages']
    system_prompt_content = bio_format_messages[0]['content']
    assert "I couldn't find any information for a trainer named \"NonExistent Trainer\"" in system_prompt_content


@pytest.mark.asyncio
async def test_trainer_bio_multiple_matches_found():
    question = "Tell me about Chris."
    
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.97)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="Chris")
    
    # API returns multiple trainers
    mock_bios_api_response_data = TrainerBioSearchResponse(results=[MOCK_CHRIS_P_BIO, MOCK_CHRIS_D_BIO], count=2)
    
    expected_llm_clarification_msg = (
        "I found a few people matching that name:\n"
        "- Chris P.\n"
        "- Chris D.\n"
        "Could you please specify which one you're interested in? Or provide their full name?"
    )
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_llm_clarification_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    with patch("app.handlers.intents_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_intent_response)), \
         patch("app.handlers.trainer_name_extractor_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_trainer_name_extraction)), \
         patch("app.repositories.trainer_bio_repository.httpx.AsyncClient.post") as mock_bios_api_post, \
         patch("app.handlers.trainer_bio_handler.OpenAIClient.chat_create", AsyncMock(return_value=mock_llm_final_response)) as mock_bio_format_openai:
        
        mock_bios_api_post.return_value = AsyncMock(
            raise_for_status=lambda: None,
            json=lambda: mock_bios_api_response_data.model_dump()
        )

        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_llm_clarification_msg
    assert data["path"] == "trainer_bio_query_result"

    mock_bios_api_post.assert_called_once_with(
        f"{os.getenv('SCHEDULING_BIOS_API_BASE_URL', 'http://mockbios.api').rstrip('/')}/v2/bios/search",
        headers=pytest.approx({
            "ocp-apim-subscription-key": os.getenv("SCHEDULING_BIOS_API_KEY", "mock_key"),
            "Content-Type": "application/json",
            "Accept": "application/json",
        }),
        json={"query": "Chris", "isPublished": True, "take": 3},
        timeout=15.0
    )
    bio_format_messages = mock_bio_format_openai.call_args[1]['messages']
    system_prompt_content = bio_format_messages[0]['content']
    assert "I found a few people matching that name:" in system_prompt_content
    assert MOCK_CHRIS_P_BIO.firstName in system_prompt_content
    assert MOCK_CHRIS_D_BIO.firstName in system_prompt_content


@pytest.mark.asyncio
async def test_trainer_bio_name_not_extracted():
    question = "Tell me about that person." # Vague question
    
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.90) # Intent might still trigger
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name=None) # Name extractor fails
    
    # No need to mock bios_api or bio_format_openai if name extraction returns None,
    # as trainer_bio_handler should short-circuit.

    with patch("app.handlers.intents_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_intent_response)), \
         patch("app.handlers.trainer_name_extractor_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_trainer_name_extraction)) as mock_name_extract_openai, \
         patch("app.repositories.trainer_bio_repository.search_trainer_bio_by_name") as mock_search_trainer_bio_by_name: # Mock the repo function directly to ensure it's not called

        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert "I couldn't quite catch the trainer's name" in data["answer"]
    assert data["path"] == "trainer_bio_query_name_missing"
    
    mock_name_extract_openai.assert_called_once()
    mock_search_trainer_bio_by_name.assert_not_called() # Crucial: API call should not happen if name is missing


@pytest.mark.asyncio
async def test_trainer_bio_api_error():
    """
    Tests how the system handles an error from the Scheduling.Bios.Api.
    """
    question = "Tell me about Sarah G."
    
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.99)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="Sarah G.")
    
    # Mock LLM response for formatting (it will receive no bio data)
    expected_llm_error_handling_msg = "I couldn't find any information for a trainer named \"Sarah G.\". 🙁\nWould you like to try a different name, or perhaps check the spelling?"
    mock_llm_final_response_choice = AsyncMock()
    mock_llm_final_response_choice.message.content = expected_llm_error_handling_msg
    mock_llm_final_response = AsyncMock(choices=[mock_llm_final_response_choice])

    with patch("app.handlers.intents_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_intent_response)), \
         patch("app.handlers.trainer_name_extractor_handler.OpenAIClient.chat_structured", AsyncMock(return_value=mock_trainer_name_extraction)), \
         patch("app.repositories.trainer_bio_repository.httpx.AsyncClient.post") as mock_bios_api_post, \
         patch("app.handlers.trainer_bio_handler.OpenAIClient.chat_create", AsyncMock(return_value=mock_llm_final_response)) as mock_bio_format_openai:

        # Simulate an API error (e.g., 500 server error)
        mock_bios_api_post.return_value = AsyncMock(
            raise_for_status=AsyncMock(side_effect=httpx.HTTPStatusError("Server Error", request=AsyncMock(), response=AsyncMock(status_code=500, text="Internal Server Error"))),
            # json call won't happen if raise_for_status fails
        )
        # Alternative for non-raising errors or if search_trainer_bio_by_name returns None on error:
        # mock_bios_api_post.return_value = AsyncMock(status_code=500, text="Internal Server Error") 
        # and then ensure search_trainer_bio_by_name handles this by returning None

        payload = {"question": question, "history": [], "scratch_pad": DEFAULT_SCRATCH_PAD}
        response = client.post("/v1/chats", json=payload)

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == expected_llm_error_handling_msg # LLM should get None for trainer_bios
    assert data["path"] == "trainer_bio_query_result"
    
    mock_bios_api_post.assert_called_once()
    bio_format_messages = mock_bio_format_openai.call_args[1]['messages']
    system_prompt_content = bio_format_messages[0]['content']
    assert "I couldn't find any information for a trainer named \"Sarah G.\"" in system_prompt_content # Jinja template should render this part