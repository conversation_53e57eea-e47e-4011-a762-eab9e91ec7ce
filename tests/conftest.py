from unittest.mock import AsyncMock, MagicMock, patch

import pytest


# Mock the services used in the application
@pytest.fixture(autouse=True)
def mock_services():
    # Create mock objects for all services
    with (
        patch("app.main.Redis") as mock_redis,
        patch("app.main.KafkaClient") as mock_kafka,
        patch("app.main.MongoDBClient") as mock_mongo,
        patch("app.main.LaunchDarklyClient") as mock_ld,
        patch("app.main.OpenAIClient") as mock_openai,
        patch("app.main.initialize_function_graph") as mock_init_graph,
    ):
        # Configure the mocks as needed
        mock_redis.connect = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_openai.connect = AsyncMock()
        mock_openai.close = AsyncMock()
        mock_mongo.connect = MagicMock()
        mock_mongo.close = MagicMock()
        mock_ld.connect = MagicMock()
        mock_ld.close = MagicMock()
        mock_kafka.kafka_conf = MagicMock()

        yield {
            "redis": mock_redis,
            "kafka": mock_kafka,
            "mongo": mock_mongo,
            "ld": mock_ld,
            "openai": mock_openai,
            "init_graph": mock_init_graph,
        }
