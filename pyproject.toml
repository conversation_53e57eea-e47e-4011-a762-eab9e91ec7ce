[project]
name = "ai-members-scheduling-agent"
version = "0.1.0"
description = "AI Members Scheduling Agent"
readme = "README.md"
requires-python = ">=3.11,<3.12"
license = {text = "Proprietary"}
authors = [
    {name = "Life Time Inc."}
]

dependencies = [
    "ai-agent-utils==0.1.26",
    "python-dotenv==1.0.1",
    "jinja2==3.1.6",
    "openai==1.55.3",
    "cachetools==5.5.1",
    "confluent_kafka==2.8.0",
    "launchdarkly-server-sdk==9.9.0",
    "PyJWT==2.8.0",
    "pymongo==4.11.1",
    "ddtrace==3.1.0",
    "datadog==0.51.0",
    "websockets==14.2",
    "fastapi==0.115.6",
    "uvicorn==0.34.0",
    "gunicorn==20.1.0",
    "pydantic==2.10.6",
    "httpx==0.28.1",
    "redis==5.2.1",
    "aiohttp==3.10.11",
    "keyrings.alt",
]

[project.optional-dependencies]
dev = [
    "ruff==0.9.3",
    "pytest==8.3.4",
    "pytest-asyncio",
    "pytest-timeout",
    "pytest-cov==6.0.0",
    "httpx",
    "pre-commit",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
select = ["E", "F", "I"]
ignore = ["E501"]

[tool.pytest.ini_options]
pythonpath = ["./app"]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "asyncio: mark a test as an asyncio test",
    "integration: mark a test as an integration test",
    "unit: mark a test as a unit test",
]
addopts = ["-v"]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"

[[tool.uv.index]]
name = "nexus"
url = "https://nexus.lifetime.life/repository/lttpip-public/simple/"
default = false
authenticate = "always"

[tool.uv.sources]
ai-agent-utils = { index = "nexus" }
